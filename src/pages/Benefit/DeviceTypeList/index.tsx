import { benefitSuitableDeviceTypeOptions } from '@/api/benefit/utils';
import { postMessageFunction } from '@/models/common.util';
import useUrlState from '@ahooksjs/use-url-state';
import { ProCard } from '@ant-design/pro-components';
import { useEffect, useState } from 'react';
import { UrlParam } from '../interface';
import { allOption } from '../util';

const DeviceTypeList = () => {
  const [urlParams] = useUrlState<UrlParam>();
  const [selectedType, setSelectedType] = useState<string>(
    `${allOption.value}`,
  );

  const selectDeviceType = (type: string) => {
    setSelectedType(type);
    // setUrlParams({
    //   deviceType: type,
    // });
    // return;
    postMessageFunction({
      type: 'redirect',
      content: {
        redirectUrl: `/business/skuBenefit?deviceType=${type}`,
      },
    });
  };

  // 初始化URL参数和选中状态
  useEffect(() => {
    // 如果URL中已经存在deviceType参数，使用现有值并更新选中状态
    if (urlParams?.deviceType) {
      setSelectedType(urlParams.deviceType);
    } else {
      // 如果URL中不存在deviceType参数，默认设置为'ALL'
      // setUrlParams({
      //   deviceType: allOption.value,
      // });
      setSelectedType(`${allOption.value}`);
    }
  }, []); // 只在组件挂载时执行一次

  return (
    <ProCard
      style={{
        borderRadius: '10px',
        height: '100%',
      }}
    >
      <div
        key={allOption.value}
        style={{
          color: '#333',
          padding: '8px 16px',
          borderRadius: '10px',
          fontSize: '14px',
          marginBottom: '8px',
          cursor: 'pointer',
          ...(selectedType === allOption.value
            ? { color: '#1890ff', background: 'rgba(24, 144, 255, 0.1)' }
            : {}),
        }}
        onClick={() => selectDeviceType(`${allOption.value}`)}
      >
        {allOption.label}
      </div>

      {benefitSuitableDeviceTypeOptions.map((option) => (
        <div
          key={option.value}
          style={{
            color: '#333',
            padding: '8px 16px',
            borderRadius: '10px',
            fontSize: '14px',
            marginBottom: '8px',
            cursor: 'pointer',
            ...(selectedType === option.value
              ? { color: '#1890ff', background: 'rgba(24, 144, 255, 0.1)' }
              : {}),
          }}
          onClick={() => selectDeviceType(`${option.value}`)}
        >
          {option.label}
        </div>
      ))}
    </ProCard>
  );
};

export default DeviceTypeList;
